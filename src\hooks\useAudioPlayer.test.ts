import { renderHook, act } from '@testing-library/react';
import { useAudioPlayer } from './useAudioPlayer';

const mockAudioInstance = {
  play: jest.fn().mockResolvedValue(undefined), // Return a resolved promise
  pause: jest.fn(),
  load: jest.fn(),
  addEventListener: jest.fn((event, cb) => {
    if (event === 'canplay') {
      // Immediately invoke the callback for 'canplay' to simulate audio loading
      cb();
    }
  }),
  removeEventListener: jest.fn(),
  duration: 100,
  currentTime: 0,
  src: '',
  volume: 1,
  paused: false, // Add paused property for wasPlaying check
};

global.Audio = jest.fn().mockImplementation(() => mockAudioInstance);

describe('useAudioPlayer', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset properties on the mock instance
    mockAudioInstance.currentTime = 0;
    mockAudioInstance.src = '';
    mockAudioInstance.volume = 1;
    mockAudioInstance.paused = false;
  });

  it('should initialize with default values', () => {
    const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));
    expect(result.current.isPlaying).toBe(false);
    expect(result.current.currentTime).toBe(0);
    expect(result.current.duration).toBe(0);
  });

  it('should call play', () => {
    const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));
    act(() => {
      result.current.play();
    });
    expect(mockAudioInstance.play).toHaveBeenCalled();
  });

  it('should call pause', () => {
    const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));
    act(() => {
      result.current.pause();
    });
    expect(mockAudioInstance.pause).toHaveBeenCalled();
  });

  it('should change source and preserve time', () => {
    const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));

    // Simulate some playback time
    act(() => {
      mockAudioInstance.currentTime = 50;
    });

    act(() => {
      result.current.changeSource('new.mp3');
    });

    expect(mockAudioInstance.src).toBe('new.mp3');
    expect(mockAudioInstance.load).toHaveBeenCalled();
    // The 'canplay' event listener in the mock will fire, setting the time
    expect(mockAudioInstance.currentTime).toBe(50);
    // It should also play after changing source by default
    expect(mockAudioInstance.play).toHaveBeenCalled();
  });

  it('should update volume without re-initializing the player', () => {
    const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));

    // The Audio constructor should be called once on initialization
    expect(global.Audio).toHaveBeenCalledTimes(1);
    expect(result.current.volume).toBe(1);
    expect(mockAudioInstance.volume).toBe(1);

    // Change the volume
    act(() => {
      result.current.setVolume(0.5);
    });

    // Assert that the volume state and the audio element's volume are updated
    expect(result.current.volume).toBe(0.5);
    expect(mockAudioInstance.volume).toBe(0.5);

    // Crucially, assert that the Audio constructor was not called again
    expect(global.Audio).toHaveBeenCalledTimes(1);
  });
});



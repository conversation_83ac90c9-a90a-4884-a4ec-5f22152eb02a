import { renderHook, act } from '@testing-library/react';
import { useAudioPlayer } from './useAudioPlayer';

const mockAudioInstance = {
  play: jest.fn().mockResolvedValue(undefined), // Return a resolved promise
  pause: jest.fn(),
  load: jest.fn(),
  addEventListener: jest.fn((event, cb) => {
    if (event === 'canplay') {
      // Immediately invoke the callback for 'canplay' to simulate audio loading
      cb();
    }
  }),
  removeEventListener: jest.fn(),
  duration: 100,
  currentTime: 0,
  src: '',
  volume: 1,
  paused: false, // Add paused property for wasPlaying check
};

global.Audio = jest.fn().mockImplementation(() => mockAudioInstance);

describe('useAudioPlayer', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset properties on the mock instance
    mockAudioInstance.currentTime = 0;
    mockAudioInstance.src = '';
    mockAudioInstance.volume = 1;
    mockAudioInstance.paused = false;
  });

  it('should initialize with default values', () => {
    const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));
    expect(result.current.isPlaying).toBe(false);
    expect(result.current.currentTime).toBe(0);
    expect(result.current.duration).toBe(0);
  });

  it('should call play', () => {
    const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));
    act(() => {
      result.current.play();
    });
    expect(mockAudioInstance.play).toHaveBeenCalled();
  });

  it('should call pause', () => {
    const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));
    act(() => {
      result.current.pause();
    });
    expect(mockAudioInstance.pause).toHaveBeenCalled();
  });

  it('should change source and preserve time', async () => {
    const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));

    // Simulate some playback time
    act(() => {
      mockAudioInstance.currentTime = 50;
    });

    act(() => {
      result.current.changeSource('new.mp3');
    });

    // Wait for the timeout in changeSource
    await new Promise(resolve => setTimeout(resolve, 60));

    expect(mockAudioInstance.src).toContain('new.mp3');
    expect(mockAudioInstance.load).toHaveBeenCalled();
    // The 'canplay' event listener in the mock will fire, setting the time
    expect(mockAudioInstance.currentTime).toBe(50);
    // It should also play after changing source by default
    expect(mockAudioInstance.play).toHaveBeenCalled();
  });

  it('should change source even when new source is the same as current', async () => {
    const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));

    // Set the source to the same value
    act(() => {
      mockAudioInstance.src = 'test.mp3';
    });

    act(() => {
      result.current.changeSource('test.mp3');
    });

    // Wait for the timeout in changeSource
    await new Promise(resolve => setTimeout(resolve, 60));

    // Should still call load and update the source (with cache busting)
    expect(mockAudioInstance.src).toContain('test.mp3');
    expect(mockAudioInstance.load).toHaveBeenCalled();
    expect(mockAudioInstance.play).toHaveBeenCalled();
  });

  it('should handle source change with playWhenReady=false', async () => {
    const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));

    act(() => {
      mockAudioInstance.currentTime = 30;
      mockAudioInstance.paused = false; // Was playing
    });

    act(() => {
      result.current.changeSource('new.mp3', false);
    });

    // Wait for the timeout in changeSource
    await new Promise(resolve => setTimeout(resolve, 60));

    expect(mockAudioInstance.src).toContain('new.mp3');
    expect(mockAudioInstance.load).toHaveBeenCalled();
    expect(mockAudioInstance.currentTime).toBe(30);
    // Should NOT play when playWhenReady is false
    expect(mockAudioInstance.play).not.toHaveBeenCalled();
  });

  it('should handle audio loading errors gracefully', async () => {
    const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    // Mock addEventListener to trigger error event after the timeout
    mockAudioInstance.addEventListener = jest.fn((event, cb) => {
      if (event === 'error') {
        // Simulate error during loading after the timeout
        setTimeout(() => cb(), 60);
      }
    });

    act(() => {
      result.current.changeSource('invalid.mp3');
    });

    // Wait for the timeout in changeSource plus the error callback
    await new Promise(resolve => setTimeout(resolve, 120));

    expect(consoleSpy).toHaveBeenCalledWith('[AudioPlayer] Failed to load audio source:', 'invalid.mp3');
    consoleSpy.mockRestore();
  });

  it('should update volume without re-initializing the player', () => {
    const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));

    // The Audio constructor should be called once on initialization
    expect(global.Audio).toHaveBeenCalledTimes(1);
    expect(result.current.volume).toBe(1);
    expect(mockAudioInstance.volume).toBe(1);

    // Change the volume
    act(() => {
      result.current.setVolume(0.5);
    });

    // Assert that the volume state and the audio element's volume are updated
    expect(result.current.volume).toBe(0.5);
    expect(mockAudioInstance.volume).toBe(0.5);

    // Crucially, assert that the Audio constructor was not called again
    expect(global.Audio).toHaveBeenCalledTimes(1);
  });
});



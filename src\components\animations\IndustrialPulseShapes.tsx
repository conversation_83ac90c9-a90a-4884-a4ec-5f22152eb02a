'use client';

import React, { useMemo, useEffect } from 'react';
import { motion, useTransform, useMotionValue, MotionValue } from 'framer-motion';
import { random } from '@/lib/random';

interface IndustrialPulseShapesProps {
  progress: number;
}

interface SmokeParticleProps {
  x: number;
  y: number;
  size: number;
  duration: number;
  delay: number;
}

const SmokeParticle: React.FC<SmokeParticleProps> = ({ x, y, size, duration, delay }) => {
  const pathVariants = {
    hidden: { opacity: 0, pathLength: 0 },
    visible: { opacity: [0, 0.6, 0.6, 0], pathLength: 1 },
  };

  const randomX = random(-10, 10);
  const randomY = random(20, 40);

  return (
    <motion.path
      d={`M ${x},${y} q ${randomX},-${randomY / 2} 0,-${randomY}`}
      fill="none"
      stroke="rgba(60, 60, 60, 0.4)"
      strokeWidth={size}
      strokeLinecap="round"
      variants={pathVariants}
      initial="hidden"
      animate="visible"
      transition={{ duration, delay, ease: 'easeInOut' }}
    />
  );
};

interface FactoryProps {
  path: string;
  initialY: number;
  finalY: number;
  progress: MotionValue<number>;
}

const Factory: React.FC<FactoryProps> = ({ path, initialY, finalY, progress }) => {
  const y = useTransform(progress, [0, 1], [initialY, finalY]);
  return <motion.path d={path} fill="#1C1C1C" style={{ y }} />;
};

const IndustrialPulseShapes: React.FC<IndustrialPulseShapesProps> = ({ progress }) => {
  const motionProgress = useMotionValue(0);
  useEffect(() => {
    motionProgress.set(progress);
  }, [progress, motionProgress]);

  const factories = useMemo(
    () => [
      { id: 1, path: 'M 10,100 L 10,70 L 15,70 L 15,65 L 25,65 L 25,70 L 30,70 L 30,100 Z', initialY: 30, finalY: 0, startProgress: 0 },
      { id: 2, path: 'M 35,100 L 35,60 L 40,60 L 40,50 L 42,50 L 42,100 Z', initialY: 40, finalY: 0, startProgress: 0.1 },
      { id: 3, path: 'M 50,100 L 50,80 L 60,80 L 60,55 L 65,55 L 65,100 Z', initialY: 20, finalY: 0, startProgress: 0.2 },
      { id: 4, path: 'M 70,100 L 70,50 L 72,50 L 72,45 L 78,45 L 78,50 L 80,50 L 80,100 Z', initialY: 50, finalY: 0, startProgress: 0.05 },
      { id: 5, path: 'M 85,100 L 85,75 L 95,75 L 95,100 Z', initialY: 25, finalY: 0, startProgress: 0.15 },
    ],
    []
  );

  const smokeEmitters = useMemo(
    () => [
      { x: 20, y: 65 },
      { x: 41, y: 50 },
      { x: 62.5, y: 55 },
      { x: 75, y: 45 },
    ],
    []
  );

  const smokeParticles = useMemo(() => {
    return Array.from({ length: 80 }).map((_, i) => {
      const emitter = smokeEmitters[i % smokeEmitters.length];
      return {
        id: i,
        x: emitter.x + random(-1, 1),
        y: emitter.y,
        size: random(2, 5),
        duration: random(5, 12),
        delay: i * 0.2,
      };
    });
  }, [smokeEmitters]);

  return (
    <div
      className="absolute inset-0 w-full h-full"
      style={{ mixBlendMode: 'soft-light' }}
    >
      <svg
        viewBox="0 0 100 100"
        className="w-full h-full"
        preserveAspectRatio="none"
      >
        <g>
          {factories.map(factory => {
            const factoryProgress = useTransform(motionProgress, [factory.startProgress, 1], [0, 1]);
            return progress > factory.startProgress ? (
              <Factory
                key={factory.id}
                path={factory.path}
                initialY={factory.initialY}
                finalY={factory.finalY}
                progress={factoryProgress}
              />
            ) : null;
          })}
        </g>
        <g>
          {progress > 0.2 &&
            smokeParticles.map(p => <SmokeParticle key={p.id} {...p} />)}
        </g>
      </svg>
    </div>
  );
};

export default IndustrialPulseShapes;

'use client';

import React, { useState, useEffect } from 'react';
import { useAudioPlayer } from '@/hooks/useAudioPlayer';
import { Button } from '@/components/ui/button';

const AUDIO_UTOPIA = '/audio/Timeline_Utopia.mp3';
const AUDIO_DYSTOPIA = '/audio/Timeline_Dystopia.mp3';

export default function DebugAudioPage() {
  const [activeTrack, setActiveTrack] = useState(AUDIO_DYSTOPIA);
  const [debugLog, setDebugLog] = useState<string[]>([]);

  const { isPlaying, currentTime, duration, play, pause, changeSource } = useAudioPlayer({
    initialSrc: activeTrack,
  });

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `[${timestamp}] ${message}`;
    console.log(logEntry);
    setDebugLog(prev => [...prev.slice(-20), logEntry]); // Keep last 20 entries
  };

  useEffect(() => {
    addLog(`Audio player initialized with: ${activeTrack}`);
  }, []);

  useEffect(() => {
    addLog(`Current time: ${currentTime.toFixed(1)}s, Duration: ${duration.toFixed(1)}s, Playing: ${isPlaying}`);
  }, [currentTime, duration, isPlaying]);

  const handleSwitchToUtopia = () => {
    addLog('=== SWITCHING TO UTOPIA ===');
    addLog(`Before switch - activeTrack: ${activeTrack}`);
    addLog(`Before switch - currentTime: ${currentTime}s`);
    
    try {
      changeSource(AUDIO_UTOPIA);
      setActiveTrack(AUDIO_UTOPIA);
      addLog('Switch to utopia initiated successfully');
    } catch (error) {
      addLog(`Error switching to utopia: ${error}`);
    }
  };

  const handleSwitchToDystopia = () => {
    addLog('=== SWITCHING TO DYSTOPIA ===');
    addLog(`Before switch - activeTrack: ${activeTrack}`);
    addLog(`Before switch - currentTime: ${currentTime}s`);
    
    try {
      changeSource(AUDIO_DYSTOPIA);
      setActiveTrack(AUDIO_DYSTOPIA);
      addLog('Switch to dystopia initiated successfully');
    } catch (error) {
      addLog(`Error switching to dystopia: ${error}`);
    }
  };

  const handleSeekTo8Minutes = () => {
    const seekTime = 480; // 8 minutes
    addLog(`Seeking to ${seekTime}s (8 minutes)`);
    
    // Use the audio element directly for seeking
    const audioElement = document.querySelector('audio');
    if (audioElement) {
      audioElement.currentTime = seekTime;
      addLog(`Seek completed to ${seekTime}s`);
    } else {
      addLog('No audio element found for seeking');
    }
  };

  const clearLog = () => {
    setDebugLog([]);
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Audio Debug Page</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Controls */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Audio Controls</h2>
            
            <div className="space-y-2">
              <p><strong>Current Track:</strong> {activeTrack.split('/').pop()}</p>
              <p><strong>Time:</strong> {currentTime.toFixed(1)}s / {duration.toFixed(1)}s</p>
              <p><strong>Status:</strong> {isPlaying ? 'Playing' : 'Paused'}</p>
            </div>

            <div className="space-y-2">
              <Button
                onClick={isPlaying ? pause : play}
                className="w-full bg-blue-600 hover:bg-blue-700"
              >
                {isPlaying ? 'Pause' : 'Play'}
              </Button>
              
              <Button
                onClick={handleSeekTo8Minutes}
                className="w-full bg-green-600 hover:bg-green-700"
              >
                Seek to 8 Minutes (Choice Time)
              </Button>
            </div>

            <div className="space-y-2">
              <Button
                onClick={handleSwitchToUtopia}
                className="w-full bg-sky-600 hover:bg-sky-700"
              >
                Switch to Utopia Audio
              </Button>
              
              <Button
                onClick={handleSwitchToDystopia}
                className="w-full bg-red-600 hover:bg-red-700"
              >
                Switch to Dystopia Audio
              </Button>
            </div>

            <Button
              onClick={clearLog}
              className="w-full bg-gray-600 hover:bg-gray-700"
            >
              Clear Debug Log
            </Button>
          </div>

          {/* Debug Log */}
          <div>
            <h2 className="text-xl font-semibold mb-4">Debug Log</h2>
            <div className="bg-black p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm">
              {debugLog.length === 0 ? (
                <p className="text-gray-500">No log entries yet...</p>
              ) : (
                debugLog.map((entry, index) => (
                  <div key={index} className="mb-1">
                    {entry}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>

        <div className="mt-8 p-4 bg-gray-800 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Instructions:</h3>
          <ol className="list-decimal list-inside space-y-1 text-sm">
            <li>Click "Play" to start audio playback</li>
            <li>Click "Seek to 8 Minutes" to jump to the choice corridor time</li>
            <li>Try switching between Utopia and Dystopia audio</li>
            <li>Watch the debug log to see what's happening</li>
            <li>Listen carefully to ensure only one audio track plays at a time</li>
          </ol>
        </div>
      </div>
    </div>
  );
}

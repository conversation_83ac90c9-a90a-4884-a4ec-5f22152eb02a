'use client';

import React, { useMemo } from 'react';
import { motion } from 'framer-motion';

interface AgricultureAndCitiesShapesProps {
  progress: number;
}

// A more curated, harmonious palette for a sophisticated, earthy feel
const settlementColors = [
  '#D2B48C', // Tan
  '#BC8F8F', // RosyBrown
  '#F4A460', // SandyBrown
  '#CD853F', // Peru
  '#A0522D', // Sienna
];

// A softer, glowing path color
const pathColor = 'rgba(245, 222, 179, 0.5)'; // Wheat with opacity

// Helper to create a random, organic blob-like SVG path
const createBlobPath = (x: number, y: number, size: number): string => {
  const numPoints = 12;
  const angleStep = (Math.PI * 2) / numPoints;
  const irregularity = 0.5; // Increased irregularity for more organic shapes

  const points = Array.from({ length: numPoints }, (_, i) => {
    const angle = i * angleStep;
    const radius = size * (1 + (Math.random() - 0.5) * 2 * irregularity);
    return {
      x: x + Math.cos(angle) * radius,
      y: y + Math.sin(angle) * radius,
    };
  });

  let d = `M ${points[0].x} ${points[0].y}`;
  for (let i = 0; i < numPoints; i++) {
    const p1 = points[i];
    const p2 = points[(i + 1) % numPoints];
    const midX = (p1.x + p2.x) / 2;
    const midY = (p1.y + p2.y) / 2;
    d += ` Q ${p1.x},${p1.y} ${midX},${midY}`;
  }
  d += ' Z';
  return d;
};



type Settlement = { id: string; x: number; y: number; d: string; color: string };
type Path = { id: string; d: string; s1_index: number; s2_index: number };

const poissonDiscSample = (width: number, height: number, minRadius: number, k: number = 30): { x: number; y: number }[] => {
  const samples: { x: number; y: number }[] = [];
  const activeSamples: { x: number; y: number }[] = [];
  const grid: ({ x: number; y: number } | null)[][] = Array.from({ length: Math.ceil(width / minRadius) }, () =>
    Array(Math.ceil(height / minRadius)).fill(null)
  );
  const cellSize = width / grid.length;

  const initialSample = { x: Math.random() * width, y: Math.random() * height };
  samples.push(initialSample);
  activeSamples.push(initialSample);
  grid[Math.floor(initialSample.x / cellSize)][Math.floor(initialSample.y / cellSize)] = initialSample;

  while (activeSamples.length > 0) {
    const randomIndex = Math.floor(Math.random() * activeSamples.length);
    const currentSample = activeSamples[randomIndex];
    let found = false;

    for (let i = 0; i < k; i++) {
      const angle = Math.random() * 2 * Math.PI;
      const radius = minRadius * (1 + Math.random());
      const newSample = {
        x: currentSample.x + Math.cos(angle) * radius,
        y: currentSample.y + Math.sin(angle) * radius,
      };

      if (newSample.x >= 0 && newSample.x < width && newSample.y >= 0 && newSample.y < height) {
        const gridX = Math.floor(newSample.x / cellSize);
        const gridY = Math.floor(newSample.y / cellSize);
        let tooClose = false;

        for (let dx = -1; dx <= 1; dx++) {
          for (let dy = -1; dy <= 1; dy++) {
            if (gridX + dx >= 0 && gridX + dx < grid.length && gridY + dy >= 0 && gridY + dy < grid[0].length) {
              const neighbor = grid[gridX + dx][gridY + dy];
              if (neighbor && Math.hypot(neighbor.x - newSample.x, neighbor.y - newSample.y) < minRadius) {
                tooClose = true;
                break;
              }
            }
          }
          if (tooClose) break;
        }

        if (!tooClose) {
          samples.push(newSample);
          activeSamples.push(newSample);
          grid[gridX][gridY] = newSample;
          found = true;
          break;
        }
      }
    }

    if (!found) {
      activeSamples.splice(randomIndex, 1);
    }
  }

  return samples;
};

const createComplexCurve = (p1: { x: number; y: number }, p2: { x: number; y: number }): string => {
  const dist = Math.hypot(p1.x - p2.x, p1.y - p2.y);
  const curveFactor = dist * 0.4; // How much the curve bows out

  // Vector from p1 to p2
  const vecX = p2.x - p1.x;
  const vecY = p2.y - p1.y;

  // Perpendicular vector
  const perpX = -vecY;
  const perpY = vecX;

  // Normalize perpendicular vector
  const len = Math.hypot(perpX, perpY);
  const normPerpX = len === 0 ? 0 : perpX / len;
  const normPerpY = len === 0 ? 0 : perpY / len;

  // Control points
  const c1 = {
    x: p1.x + vecX * 0.25 + normPerpX * curveFactor * (Math.random() - 0.5),
    y: p1.y + vecY * 0.25 + normPerpY * curveFactor * (Math.random() - 0.5),
  };
  const c2 = {
    x: p2.x - vecX * 0.25 + normPerpX * curveFactor * (Math.random() - 0.5),
    y: p2.y - vecY * 0.25 + normPerpY * curveFactor * (Math.random() - 0.5),
  };

  return `M ${p1.x} ${p1.y} C ${c1.x} ${c1.y}, ${c2.x} ${c2.y}, ${p2.x} ${p2.y}`;
};

const AgricultureAndCitiesShapes: React.FC<AgricultureAndCitiesShapesProps> = ({ progress }) => {
  const { settlements, paths } = useMemo(() => {
    const settlementList: Settlement[] = [];
    const pathList: Path[] = [];

    // 1. Generate evenly distributed points
    const points = poissonDiscSample(90, 90, 15, 30).map(p => ({ x: p.x + 5, y: p.y + 5 }));

    // 2. Create settlements from these points and randomize their animation order
    points.forEach((point, i) => {
      const size = 0.8 + Math.random() * 1.2;
      const color = settlementColors[Math.floor(Math.random() * settlementColors.length)];
      settlementList.push({
        id: `s-${i}`,
        x: point.x,
        y: point.y,
        d: createBlobPath(point.x, point.y, size),
        color,
      });
    });
    settlementList.sort(() => Math.random() - 0.5);

    // 3. Create a rich network of paths between all nearby settlements
    const connectionThreshold = 35; // Increased threshold for more connections
    for (let i = 0; i < settlementList.length; i++) {
      for (let j = i + 1; j < settlementList.length; j++) {
        const s1 = settlementList[i];
        const s2 = settlementList[j];
        const dist = Math.hypot(s1.x - s2.x, s1.y - s2.y);

        if (dist < connectionThreshold) {
          pathList.push({
            id: `p-${s1.id}-${s2.id}`,
            d: createComplexCurve({ x: s1.x, y: s1.y }, { x: s2.x, y: s2.y }),
            s1_index: i, // Use the final animation indices
            s2_index: j,
          });
        }
      }
    }

    return { settlements: settlementList, paths: pathList };
  }, []);

  const visibleSettlementsCount = Math.floor(progress * settlements.length);
  const pathProgress = Math.max(0, (progress - 0.1) / 0.9); // Paths start after 10% of settlements appear
  const visiblePathsCount = Math.floor(pathProgress * paths.length);

  return (
    <div className="absolute inset-0 overflow-hidden" data-testid="agriculture-cities-container">
      <svg viewBox="0 0 100 100" className="w-full h-full" preserveAspectRatio="none">
        <defs>
          <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="0.5" result="coloredBlur" />
            <feMerge>
              <feMergeNode in="coloredBlur" />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
        </defs>

        {/* Paths (Roads/Rivers) */}
        {paths.slice(0, visiblePathsCount).map((path, index) => (
          <motion.path
            key={path.id}
            d={path.d}
            stroke={pathColor}
            strokeWidth={0.2} // Thinner for a more delicate look
            fill="none"
            initial={{ pathLength: 0, opacity: 0 }}
            animate={{ pathLength: 1, opacity: 1 }}
            transition={{ duration: 4, delay: Math.max(path.s1_index, path.s2_index) * 0.4, ease: 'easeInOut' }}
            filter="url(#glow)"
          />
        ))}

        {/* Settlements */}
        {settlements.slice(0, visibleSettlementsCount).map((settlement, index) => (
          <motion.path
            key={settlement.id}
            d={settlement.d}
            fill={settlement.color}
            fillOpacity={0.7} // Semi-transparent fill
            stroke={settlement.color}
            strokeWidth={0.15}
            initial={{
              opacity: 0,
              scale: 0.2,
            }}
            animate={{
              opacity: 1,
              scale: 1,
            }}
            transition={{
              duration: 4,
              delay: index * 0.4, // Slower spawning
              ease: 'easeOut',
            }}
            filter="url(#glow)"
          />
        ))}
      </svg>
    </div>
  );
};

export default AgricultureAndCitiesShapes;

'use client';

import React, { useMemo } from 'react';
import { motion } from 'framer-motion';

interface AgricultureAndCitiesShapesProps {
  progress: number;
}

// More earthy and historically inspired tones for settlements
const settlementColors = [
  '#A0522D', // Sienna
  '#D2691E', // Chocolate
  '#8B4513', // SaddleBrown
  '#BC8F8F', // RosyBrown
  '#CD853F', // Peru
];

// Softer, more organic path color
const pathColor = 'rgba(222, 184, 135, 0.4)'; // BurlyWood with opacity

// Helper to create a random, organic blob-like SVG path
const createBlobPath = (x: number, y: number, size: number): string => {
  const numPoints = 12;
  const angleStep = (Math.PI * 2) / numPoints;
  const irregularity = 0.5; // Increased irregularity for more organic shapes

  const points = Array.from({ length: numPoints }, (_, i) => {
    const angle = i * angleStep;
    const radius = size * (1 + (Math.random() - 0.5) * 2 * irregularity);
    return {
      x: x + Math.cos(angle) * radius,
      y: y + Math.sin(angle) * radius,
    };
  });

  let d = `M ${points[0].x} ${points[0].y}`;
  for (let i = 0; i < numPoints; i++) {
    const p1 = points[i];
    const p2 = points[(i + 1) % numPoints];
    const midX = (p1.x + p2.x) / 2;
    const midY = (p1.y + p2.y) / 2;
    d += ` Q ${p1.x},${p1.y} ${midX},${midY}`;
  }
  d += ' Z';
  return d;
};



type Settlement = { id: string; x: number; y: number; d: string; color: string };
type Path = { id: string; d: string; s1_index: number; s2_index: number };

const poissonDiscSample = (width: number, height: number, minRadius: number, k: number = 30): { x: number; y: number }[] => {
  const samples: { x: number; y: number }[] = [];
  const activeSamples: { x: number; y: number }[] = [];
  const grid: ({ x: number; y: number } | null)[][] = Array.from({ length: Math.ceil(width / minRadius) }, () =>
    Array(Math.ceil(height / minRadius)).fill(null)
  );
  const cellSize = width / grid.length;

  const initialSample = { x: Math.random() * width, y: Math.random() * height };
  samples.push(initialSample);
  activeSamples.push(initialSample);
  grid[Math.floor(initialSample.x / cellSize)][Math.floor(initialSample.y / cellSize)] = initialSample;

  while (activeSamples.length > 0) {
    const randomIndex = Math.floor(Math.random() * activeSamples.length);
    const currentSample = activeSamples[randomIndex];
    let found = false;

    for (let i = 0; i < k; i++) {
      const angle = Math.random() * 2 * Math.PI;
      const radius = minRadius * (1 + Math.random());
      const newSample = {
        x: currentSample.x + Math.cos(angle) * radius,
        y: currentSample.y + Math.sin(angle) * radius,
      };

      if (newSample.x >= 0 && newSample.x < width && newSample.y >= 0 && newSample.y < height) {
        const gridX = Math.floor(newSample.x / cellSize);
        const gridY = Math.floor(newSample.y / cellSize);
        let tooClose = false;

        for (let dx = -1; dx <= 1; dx++) {
          for (let dy = -1; dy <= 1; dy++) {
            if (gridX + dx >= 0 && gridX + dx < grid.length && gridY + dy >= 0 && gridY + dy < grid[0].length) {
              const neighbor = grid[gridX + dx][gridY + dy];
              if (neighbor && Math.hypot(neighbor.x - newSample.x, neighbor.y - newSample.y) < minRadius) {
                tooClose = true;
                break;
              }
            }
          }
          if (tooClose) break;
        }

        if (!tooClose) {
          samples.push(newSample);
          activeSamples.push(newSample);
          grid[gridX][gridY] = newSample;
          found = true;
          break;
        }
      }
    }

    if (!found) {
      activeSamples.splice(randomIndex, 1);
    }
  }

  return samples;
};

const AgricultureAndCitiesShapes: React.FC<AgricultureAndCitiesShapesProps> = ({ progress }) => {
  const { settlements, paths } = useMemo(() => {
    const settlementList: Settlement[] = [];
    const pathList: Path[] = [];

    // 1. Generate evenly distributed points
    const points = poissonDiscSample(90, 90, 12, 30).map(p => ({ x: p.x + 5, y: p.y + 5 }));

    // 2. Create settlements from these points
    points.forEach((point, i) => {
      const size = 0.8 + Math.random() * 1.2;
      const color = settlementColors[Math.floor(Math.random() * settlementColors.length)];
      settlementList.push({
        id: `s-${i}`,
        x: point.x,
        y: point.y,
        d: createBlobPath(point.x, point.y, size),
        color,
      });
    });

    // 3. Create paths between closest neighbors
    const connectedSettlementIds = new Set<string>();
    const connectionThreshold = 25; // Adjusted threshold for new distribution

    for (let i = 0; i < settlementList.length; i++) {
      const s1 = settlementList[i];
      if (connectedSettlementIds.has(s1.id)) continue;

      let closestPartner: Settlement | null = null;
      let closestPartnerIndex = -1;
      let minDistance = Infinity;

      for (let j = 0; j < settlementList.length; j++) {
        if (i === j || connectedSettlementIds.has(settlementList[j].id)) continue;
        const s2 = settlementList[j];
        const dist = Math.hypot(s1.x - s2.x, s1.y - s2.y);
        if (dist < minDistance) {
          minDistance = dist;
          closestPartner = s2;
          closestPartnerIndex = j;
        }
      }

      if (closestPartner && closestPartnerIndex !== -1 && minDistance < connectionThreshold) {
        const p1 = { x: s1.x, y: s1.y };
        const p2 = { x: closestPartner.x, y: closestPartner.y };

        const midX = (p1.x + p2.x) / 2;
        const midY = (p1.y + p2.y) / 2;
        const vecX = p2.x - p1.x;
        const vecY = p2.y - p1.y;
        const perpX = -vecY;
        const perpY = vecX;
        const len = Math.hypot(perpX, perpY);
        const normPerpX = len === 0 ? 0 : perpX / len;
        const normPerpY = len === 0 ? 0 : perpY / len;
        const curveAmount = (Math.random() * 15 + 10) * (Math.random() > 0.5 ? 1 : -1);

        // Clamp control points to be within the viewport [0, 100]
        const controlX = Math.max(0, Math.min(100, midX + normPerpX * curveAmount));
        const controlY = Math.max(0, Math.min(100, midY + normPerpY * curveAmount));

        pathList.push({
          id: `p-${s1.id}-${closestPartner.id}`,
          d: `M ${p1.x} ${p1.y} Q ${controlX} ${controlY} ${p2.x} ${p2.y}`,
          s1_index: i,
          s2_index: closestPartnerIndex,
        });
        connectedSettlementIds.add(s1.id);
        connectedSettlementIds.add(closestPartner.id);
      }
    }

    // Randomize animation order
    settlementList.sort(() => Math.random() - 0.5);

    return { settlements: settlementList, paths: pathList };
  }, []);

  const visibleSettlementsCount = Math.floor(progress * settlements.length);
  const pathProgress = Math.max(0, (progress - 0.1) / 0.9); // Paths start after 10% of settlements appear
  const visiblePathsCount = Math.floor(pathProgress * paths.length);

  return (
    <div className="absolute inset-0 overflow-hidden" data-testid="agriculture-cities-container">
      <svg viewBox="0 0 100 100" className="w-full h-full" preserveAspectRatio="none">
        <defs>
          <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="0.5" result="coloredBlur" />
            <feMerge>
              <feMergeNode in="coloredBlur" />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
        </defs>

        {/* Paths (Roads/Rivers) - Thicker and more organic */}
        {paths.slice(0, visiblePathsCount).map((path, index) => (
          <motion.path
            key={path.id}
            d={path.d}
            stroke={pathColor}
            strokeWidth={0.35} // Increased thickness
            fill="none"
            initial={{ pathLength: 0, opacity: 0 }}
            animate={{ pathLength: 1, opacity: 1 }}
            transition={{ duration: 4, delay: Math.max(path.s1_index, path.s2_index) * 0.4, ease: 'easeInOut' }}
          />
        ))}

        {/* Settlements - Organic Shapes with Outlines and Glow */}
        {settlements.slice(0, visibleSettlementsCount).map(({ id, d, color }, index) => (
          <motion.path
            key={id}
            d={d}
            fill={`${color}99`} // Semi-transparent fill (~60%)
            stroke={color}
            strokeWidth={0.15}
            filter="url(#glow)"
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{
              opacity: 1,
              scale: 1,
            }}
            transition={{
              duration: 4,
              delay: index * 0.4, // Slower spawning
              ease: 'easeOut',
            }}
          />
        ))}
      </svg>
    </div>
  );
};

export default AgricultureAndCitiesShapes;

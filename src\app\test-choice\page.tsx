'use client';

import React, { useState, useEffect } from 'react';
import { useAudioPlayer } from '@/hooks/useAudioPlayer';
import ChoiceCorridor from '@/components/ChoiceCorridor';
import { Button } from '@/components/ui/button';

const AUDIO_UTOPIA = '/audio/Timeline_Utopia.mp3';
const AUDIO_DYSTOPIA = '/audio/Timeline_Dystopia.mp3';

export default function TestChoicePage() {
  const [activeTrack, setActiveTrack] = useState(AUDIO_DYSTOPIA);
  const [userChoice, setUserChoice] = useState<'utopia' | 'dystopia' | 'none'>('none');
  const [choiceMade, setChoiceMade] = useState(false);
  const [showChoice, setShowChoice] = useState(true);
  const [testLog, setTestLog] = useState<string[]>([]);

  const { isPlaying, currentTime, duration, play, pause, changeSource } = useAudioPlayer({
    initialSrc: activeTrack,
  });

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestLog(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  const handleSelectUtopia = () => {
    addLog('Utopia selection started');
    
    // Prevent multiple clicks
    if (choiceMade) {
      addLog('ERROR: Multiple click detected - choice already made');
      return;
    }
    
    // Batch all state updates together to prevent race conditions
    setUserChoice('utopia');
    setChoiceMade(true);
    setShowChoice(false);
    addLog('State updates completed: userChoice=utopia, choiceMade=true, showChoice=false');
    
    // Handle audio change separately to ensure proper cleanup
    if (activeTrack !== AUDIO_UTOPIA) {
      addLog(`Audio change: ${activeTrack} -> ${AUDIO_UTOPIA}`);
      changeSource(AUDIO_UTOPIA);
      setActiveTrack(AUDIO_UTOPIA);
      addLog('Audio source changed successfully');
    } else {
      addLog('Audio already correct - no change needed');
    }
    
    addLog('Utopia selection completed successfully');
  };

  const handleSelectDystopia = () => {
    addLog('Dystopia selection started');
    
    // Prevent multiple clicks
    if (choiceMade) {
      addLog('ERROR: Multiple click detected - choice already made');
      return;
    }
    
    // Batch all state updates together to prevent race conditions
    setUserChoice('dystopia');
    setChoiceMade(true);
    setShowChoice(false);
    addLog('State updates completed: userChoice=dystopia, choiceMade=true, showChoice=false');
    
    // Handle audio change separately to ensure proper cleanup
    if (activeTrack !== AUDIO_DYSTOPIA) {
      addLog(`Audio change: ${activeTrack} -> ${AUDIO_DYSTOPIA}`);
      changeSource(AUDIO_DYSTOPIA);
      setActiveTrack(AUDIO_DYSTOPIA);
      addLog('Audio source changed successfully');
    } else {
      addLog('Audio already correct - no change needed');
    }
    
    addLog('Dystopia selection completed successfully');
  };

  const resetTest = () => {
    setUserChoice('none');
    setChoiceMade(false);
    setShowChoice(true);
    setTestLog([]);
    addLog('Test reset - ready for new selection');
  };

  const testRapidClicks = () => {
    addLog('Testing rapid clicks...');
    // Simulate rapid clicking
    handleSelectUtopia();
    handleSelectUtopia();
    handleSelectUtopia();
    addLog('Rapid click test completed');
  };

  return (
    <div className="min-h-screen bg-black relative overflow-hidden">
      {/* Control Panel */}
      <div className="absolute top-4 left-4 z-50 bg-white/10 backdrop-blur-sm rounded-lg p-4 text-white max-w-md">
        <h1 className="text-xl font-bold mb-4">Choice Corridor Test</h1>
        
        <div className="space-y-2 mb-4 text-sm">
          <div>Active Track: {activeTrack.includes('Utopia') ? 'Utopia' : 'Dystopia'}</div>
          <div>User Choice: {userChoice}</div>
          <div>Choice Made: {choiceMade ? 'Yes' : 'No'}</div>
          <div>Show Choice: {showChoice ? 'Yes' : 'No'}</div>
          <div>Audio Playing: {isPlaying ? 'Yes' : 'No'}</div>
          <div>Current Time: {currentTime.toFixed(1)}s</div>
        </div>

        <div className="space-y-2">
          <div className="grid grid-cols-2 gap-2">
            <Button
              onClick={isPlaying ? pause : play}
              className="bg-blue-600 hover:bg-blue-700 text-white text-sm"
            >
              {isPlaying ? 'Pause' : 'Play'}
            </Button>
            
            <Button
              onClick={resetTest}
              className="bg-gray-600 hover:bg-gray-700 text-white text-sm"
            >
              Reset Test
            </Button>
          </div>
          
          <Button
            onClick={testRapidClicks}
            className="w-full bg-yellow-600 hover:bg-yellow-700 text-white text-sm"
          >
            Test Rapid Clicks
          </Button>
        </div>
      </div>

      {/* Choice Corridor */}
      {showChoice && (
        <ChoiceCorridor
          onSelectUtopia={handleSelectUtopia}
          onSelectDystopia={handleSelectDystopia}
        />
      )}

      {/* Results Display */}
      {choiceMade && (
        <div className="absolute inset-0 flex items-center justify-center z-20">
          <div className="bg-black/80 p-8 rounded-xl border border-gray-700 text-center">
            <h2 className="text-3xl font-bold text-white mb-4">
              {userChoice === 'utopia' ? '🌟 Utopia Selected' : '🔥 Dystopia Selected'}
            </h2>
            <p className="text-lg text-gray-300">
              Audio Track: {activeTrack.includes('Utopia') ? 'Utopia' : 'Dystopia'}
            </p>
            <p className="text-sm text-gray-400 mt-2">
              {activeTrack.includes(userChoice) ? '✅ Audio/Visual Match' : '❌ Audio/Visual Mismatch'}
            </p>
          </div>
        </div>
      )}

      {/* Debug Log */}
      <div className="absolute bottom-4 right-4 z-50 bg-white/10 backdrop-blur-sm rounded-lg p-4 text-white max-w-md max-h-64 overflow-y-auto">
        <h2 className="font-bold mb-2">Debug Log:</h2>
        <div className="text-xs space-y-1">
          {testLog.map((log, index) => (
            <div key={index} className={log.includes('ERROR') ? 'text-red-400' : 'text-gray-300'}>
              {log}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
